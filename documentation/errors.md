# Frontend Build & Lint Errors

## Multiple Lockfiles Warning
- Warning: Found multiple lockfiles. Selecting `/Users/<USER>/package-lock.json`.
  - Consider removing the lockfiles at:
    - `/Users/<USER>/Downloads/kickoffpredict/frontend/package-lock.json`
    - `/Users/<USER>/Downloads/kickoffpredict/package-lock.json`

## Compilation Errors & Warnings

### ./src/app/match/[...slug]/page.tsx
- Lines 15–19, 99: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Line 39: React Hook `useEffect` has a missing dependency: `liveFixtures`. Either include it or remove the dependency array. (`react-hooks/exhaustive-deps`)
- Line 198: React Hook `useEffect` has a missing dependency: `fixtureData`. Either include it or remove the dependency array. (`react-hooks/exhaustive-deps`)

### ./src/components/fixture/FixtureContent.tsx
- Lines 12–17, 87, 90: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Lines 24, 25: 'isLive' and 'isFinished' are assigned a value but never used. (`@typescript-eslint/no-unused-vars`)
- Line 90: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`. (`react/no-unescaped-entities`)

### ./src/components/fixture/FixtureHeader.tsx
- Line 5: 'addDays' is defined but never used. (`@typescript-eslint/no-unused-vars`)
- Line 92: 'getStatusDisplay' is assigned a value but never used. (`@typescript-eslint/no-unused-vars`)
- Line 121: 'minutesUntil' is assigned a value but never used. (`@typescript-eslint/no-unused-vars`)

### ./src/components/fixture/FixtureSidebar.tsx
- Lines 6, 7, 11, 12: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Lines 10, 11, 12, 13: 'predictions', 'standings', 'relatedMatches', 'loading' are assigned a value but never used. (`@typescript-eslint/no-unused-vars`)
- Lines 82, 112: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader. ([See Next.js docs](https://nextjs.org/docs/messages/no-img-element))

### ./src/components/fixture/HeadToHead.tsx
- Lines 4, 5, 19: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Line 37: 'awayTeamId' is assigned a value but never used. (`@typescript-eslint/no-unused-vars`)

### ./src/components/fixture/MatchEvents.tsx
- Lines 4, 5: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Line 64: 'eventTime' is assigned a value but never used. (`@typescript-eslint/no-unused-vars`)
- Line 71: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`. (`react/no-unescaped-entities`)
- Line 108: Using `<img>` could result in slower LCP and higher bandwidth. ([See Next.js docs](https://nextjs.org/docs/messages/no-img-element))

### ./src/components/fixture/MatchLineups.tsx
- Lines 4, 5, 30: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Lines 64, 82, 106, 124: Using `<img>` could result in slower LCP and higher bandwidth. ([See Next.js docs](https://nextjs.org/docs/messages/no-img-element))

### ./src/components/fixture/MatchStatistics.tsx
- Lines 4, 5, 186: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)

### ./src/components/fixture/TeamForm.tsx
- Line 6: Unexpected `any`. Specify a different type. (`@typescript-eslint/no-explicit-any`)
- Lines 141, 191: Using `<img>` could result in slower LCP and higher bandwidth. ([See Next.js docs](https://nextjs.org/docs/messages/no-img-element))
- Lines 149, 199: 'index' is defined but never used. (`@typescript-eslint/no-unused-vars`)

---

**Note:**
- Replace all `any` types with specific types or interfaces.
- Remove unused variables or use them if needed.
- For `<img>`, use Next.js `<Image />` for optimization.
- Address missing dependencies in `useEffect` hooks.
- Remove extra lockfiles to avoid build warnings.