'use client';

import { useVirtualizer } from '@tanstack/react-virtual';
import { useRef, useMemo } from 'react';
import { Fixture } from '@/lib/api';
import { OptimizedFixtureCard } from './OptimizedFixtureCard';
import { LeagueHeaderStatic } from './LeagueHeaderStatic';
import { groupFixturesByLeague, LeagueGroup } from '@/lib/leagueTiers';

interface VirtualizedFixtureListProps {
  fixtures: Fixture[];
  searchQuery?: string;
  selectedLeagueId?: number | null;
  className?: string;
}

interface VirtualItem {
  type: 'league-header' | 'fixture';
  data: LeagueGroup | Fixture;
  leagueGroup?: LeagueGroup; // For fixtures, reference to their league
}

export function VirtualizedFixtureList({ 
  fixtures, 
  searchQuery = '', 
  selectedLeagueId,
  className = '' 
}: VirtualizedFixtureListProps) {
  const parentRef = useRef<HTMLDivElement>(null);

  // Group fixtures by league and create virtual items
  const virtualItems = useMemo(() => {
    let filteredFixtures = fixtures;

    // Filter by selected league
    if (selectedLeagueId) {
      filteredFixtures = fixtures.filter(fixture =>
        fixture.league.id === selectedLeagueId
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredFixtures = filteredFixtures.filter(fixture =>
        fixture.teams.home.name.toLowerCase().includes(query) ||
        fixture.teams.away.name.toLowerCase().includes(query) ||
        fixture.league.name.toLowerCase().includes(query) ||
        fixture.league.country.toLowerCase().includes(query)
      );
    }

    // Group by league
    const leagueGroups = groupFixturesByLeague(filteredFixtures);
    
    // Create flat array of virtual items
    const items: VirtualItem[] = [];
    
    leagueGroups.forEach(leagueGroup => {
      // Add league header
      items.push({
        type: 'league-header',
        data: leagueGroup
      });
      
      // Add fixtures for this league
      leagueGroup.fixtures.forEach(fixture => {
        items.push({
          type: 'fixture',
          data: fixture,
          leagueGroup
        });
      });
    });

    return items;
  }, [fixtures, searchQuery, selectedLeagueId]);

  // Estimate item size based on type
  const getItemSize = (index: number) => {
    const item = virtualItems[index];
    if (item.type === 'league-header') {
      return 48; // League header height
    }
    return 80; // Fixture card height
  };

  const virtualizer = useVirtualizer({
    count: virtualItems.length,
    getScrollElement: () => parentRef.current,
    estimateSize: getItemSize,
    overscan: 5, // Render 5 extra items outside viewport for smooth scrolling
  });

  // Show empty state if no fixtures
  if (virtualItems.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <p className="text-muted-foreground text-lg mb-2">No fixtures found</p>
          {searchQuery && (
            <p className="text-muted-foreground text-sm">
              Try adjusting your search terms
            </p>
          )}
          {selectedLeagueId && !searchQuery && (
            <p className="text-muted-foreground text-sm">
              No fixtures available for this league
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className={`h-full overflow-auto ${className}`}
      style={{
        height: '600px', // Fixed height for virtualization
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const item = virtualItems[virtualItem.index];
          
          return (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              {item.type === 'league-header' ? (
                <LeagueHeaderStatic
                  leagueCountry={(item.data as LeagueGroup).leagueCountry}
                  leagueLogo={(item.data as LeagueGroup).leagueLogo}
                  leagueName={(item.data as LeagueGroup).leagueName}
                />
              ) : (
                <OptimizedFixtureCard fixture={item.data as Fixture} />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Hook for performance monitoring of virtualized list
export function useVirtualizedListPerformance(itemCount: number) {
  return useMemo(() => {
    const isLargeList = itemCount > 50;
    const shouldVirtualize = itemCount > 20;
    
    return {
      isLargeList,
      shouldVirtualize,
      estimatedMemorySaving: isLargeList ? `${Math.round((itemCount - 20) * 0.5)}KB` : '0KB',
      renderingStrategy: shouldVirtualize ? 'virtualized' : 'standard'
    };
  }, [itemCount]);
}
