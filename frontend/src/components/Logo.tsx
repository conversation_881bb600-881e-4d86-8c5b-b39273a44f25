// Server Component - No 'use client' directive needed
import Link from 'next/link';

interface LogoProps {
  className?: string;
}

export default function Logo({
  className = ""
}: LogoProps) {
  return (
    <Link href="/" className={`flex items-center ${className}`}>
      <span className="text-xl font-bold text-foreground hover:text-primary transition-colors cursor-pointer">
        KICKOFFSCORE
      </span>
    </Link>
  );
}
