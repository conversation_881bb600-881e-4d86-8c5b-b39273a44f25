'use client';

import { useState, useMemo } from 'react';
import { Fixture } from '@/lib/api';
import { SmartFixtureList } from './SmartFixtureList';

interface VirtualizationDemoProps {
  fixtures: Fixture[];
}

export function VirtualizationDemo({ fixtures }: VirtualizationDemoProps) {
  const [forceVirtualization, setForceVirtualization] = useState(false);
  const [showPerformanceInfo, setShowPerformanceInfo] = useState(true);

  // Performance metrics
  const performanceInfo = useMemo(() => {
    const fixtureCount = fixtures.length;
    const shouldAutoVirtualize = fixtureCount > 20;
    const estimatedDOMNodes = shouldAutoVirtualize ? 20 : fixtureCount;
    const estimatedMemory = fixtureCount * 2; // Rough estimate in KB
    const savedMemory = shouldAutoVirtualize ? (fixtureCount - 20) * 2 : 0;

    return {
      fixtureCount,
      shouldAutoVirtualize,
      estimatedDOMNodes,
      estimatedMemory,
      savedMemory,
      renderingMode: forceVirtualization || shouldAutoVirtualize ? 'Virtualized' : 'Standard'
    };
  }, [fixtures.length, forceVirtualization]);

  return (
    <div className="space-y-4">
      {/* Performance Info Panel */}
      {showPerformanceInfo && (
        <div className="bg-muted/50 rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm">Virtualization Performance Info</h3>
            <button
              onClick={() => setShowPerformanceInfo(false)}
              className="text-xs text-muted-foreground hover:text-foreground"
            >
              Hide
            </button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Fixtures</div>
              <div className="font-medium">{performanceInfo.fixtureCount}</div>
            </div>
            <div>
              <div className="text-muted-foreground">DOM Nodes</div>
              <div className="font-medium">{performanceInfo.estimatedDOMNodes}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Memory Saved</div>
              <div className="font-medium text-green-600">
                {performanceInfo.savedMemory > 0 ? `~${performanceInfo.savedMemory}KB` : '0KB'}
              </div>
            </div>
            <div>
              <div className="text-muted-foreground">Rendering</div>
              <div className={`font-medium ${
                performanceInfo.renderingMode === 'Virtualized' ? 'text-blue-600' : 'text-gray-600'
              }`}>
                {performanceInfo.renderingMode}
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="mt-4 pt-3 border-t border-border">
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={forceVirtualization}
                onChange={(e) => setForceVirtualization(e.target.checked)}
                className="rounded border-border"
              />
              <span>Force virtualization (for testing)</span>
            </label>
          </div>
        </div>
      )}

      {/* Fixture List */}
      <SmartFixtureList
        fixtures={fixtures}
        forceVirtualization={forceVirtualization}
        className="min-h-[400px]"
      />

      {/* Show performance info toggle if hidden */}
      {!showPerformanceInfo && (
        <button
          onClick={() => setShowPerformanceInfo(true)}
          className="text-xs text-muted-foreground hover:text-foreground underline"
        >
          Show performance info
        </button>
      )}
    </div>
  );
}
