'use client';

import { Fixture } from '@/lib/api';
import { VirtualizedFixtureList, useVirtualizedListPerformance } from './VirtualizedFixtureList';
import { StandardFixtureList } from './StandardFixtureList';

interface SmartFixtureListProps {
  fixtures: Fixture[];
  searchQuery?: string;
  selectedLeagueId?: number | null;
  loading?: boolean;
  className?: string;
  forceVirtualization?: boolean; // For testing purposes
}

export function SmartFixtureList({ 
  fixtures, 
  searchQuery = '', 
  selectedLeagueId,
  loading = false,
  className = '',
  forceVirtualization = false
}: SmartFixtureListProps) {
  const { shouldVirtualize, renderingStrategy, estimatedMemorySaving } = 
    useVirtualizedListPerformance(fixtures.length);

  // Show loading state
  if (loading) {
    return <FixtureListSkeleton className={className} />;
  }

  // Decide whether to use virtualization
  const useVirtualization = forceVirtualization || shouldVirtualize;

  // Log performance info in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`FixtureList: ${fixtures.length} items, strategy: ${renderingStrategy}, memory saving: ${estimatedMemorySaving}`);
  }

  return useVirtualization ? (
    <VirtualizedFixtureList
      fixtures={fixtures}
      searchQuery={searchQuery}
      selectedLeagueId={selectedLeagueId}
      className={className}
    />
  ) : (
    <StandardFixtureList
      fixtures={fixtures}
      searchQuery={searchQuery}
      selectedLeagueId={selectedLeagueId}
      className={className}
    />
  );
}

// Loading skeleton for fixture list
function FixtureListSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Create 3 skeleton league containers */}
      {Array.from({ length: 3 }).map((_, leagueIndex) => (
        <div key={`league-skeleton-${leagueIndex}`} className="bg-card rounded-lg border container-border overflow-hidden">
          {/* League Header Skeleton */}
          <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
            <div className="w-[18px] h-[18px] bg-muted-foreground/20 rounded-full animate-pulse"></div>
            <div className="h-4 bg-muted-foreground/20 rounded animate-pulse w-48"></div>
          </div>
          {/* Skeleton fixture cards for this league */}
          {Array.from({ length: 2 + leagueIndex }).map((_, fixtureIndex) => (
            <FixtureCardSkeleton
              key={`fixture-skeleton-${leagueIndex}-${fixtureIndex}`}
              isLast={fixtureIndex === 1 + leagueIndex}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

// Individual fixture card skeleton
function FixtureCardSkeleton({ isLast = false }: { isLast?: boolean }) {
  return (
    <div className={`flex items-center justify-between p-4 animate-pulse ${!isLast ? 'border-b border-border' : ''}`}>
      {/* Home Team Skeleton */}
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <div className="w-8 h-8 bg-muted rounded flex-shrink-0"></div>
        <div className="flex-1 min-w-0">
          <div className="h-4 bg-muted rounded w-24"></div>
        </div>
      </div>

      {/* Score/Time Skeleton */}
      <div className="text-center px-4 min-w-[80px]">
        <div className="space-y-1">
          <div className="h-6 bg-muted rounded w-12 mx-auto"></div>
          <div className="h-3 bg-muted rounded w-8 mx-auto"></div>
        </div>
      </div>

      {/* Away Team Skeleton */}
      <div className="flex items-center space-x-3 flex-1 min-w-0 justify-end">
        <div className="flex-1 min-w-0 text-right">
          <div className="h-4 bg-muted rounded w-24 ml-auto"></div>
        </div>
        <div className="w-8 h-8 bg-muted rounded flex-shrink-0"></div>
      </div>
    </div>
  );
}
