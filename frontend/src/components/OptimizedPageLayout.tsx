// Server Component for the main page layout
import Footer from '@/components/Footer';
import { PageClientWrapper } from '@/components/PageClientWrapper';

interface OptimizedPageLayoutProps {
  children?: React.ReactNode;
}

export function OptimizedPageLayout({ children }: OptimizedPageLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* SEO H1 Title - Hidden visually but accessible to search engines */}
      <h1 className="sr-only">
        Kickoffscore - livescore and schedule for Premier League, Champions League
      </h1>

      {/* Client-side wrapper handles Header with state and all interactive parts */}
      <PageClientWrapper>
        {children}
      </PageClientWrapper>

      {/* Server-rendered Footer */}
      <Footer />
    </div>
  );
}
