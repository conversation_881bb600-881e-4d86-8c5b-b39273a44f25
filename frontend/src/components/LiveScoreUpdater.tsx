'use client';

import { useEffect, useState } from 'react';
import { Fixture } from '@/lib/api';
import { useLiveFixtures } from '@/hooks/useSocket';

interface LiveScoreUpdaterProps {
  fixture: Fixture;
  onUpdate?: (updatedFixture: Fixture) => void;
}

export function LiveScoreUpdater({ fixture, onUpdate }: LiveScoreUpdaterProps) {
  const [currentFixture, setCurrentFixture] = useState(fixture);
  const { liveFixtures } = useLiveFixtures();

  // Update fixture when live data comes in
  useEffect(() => {
    const liveUpdate = liveFixtures.find(
      liveFixture => liveFixture._id === fixture._id || liveFixture.fixture.id === fixture.fixture.id
    );

    if (liveUpdate) {
      setCurrentFixture(liveUpdate);
      onUpdate?.(liveUpdate);
    }
  }, [liveFixtures, fixture._id, fixture.fixture.id, onUpdate]);

  // Format time for display
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Get status display with live updates
  const getStatusDisplay = () => {
    const status = currentFixture.fixture.status;
    if (status.short === 'NS') return formatTime(currentFixture.fixture.timestamp);
    if (status.short === 'FT') return 'FT';
    if (status.short === 'HT') return 'HT';
    if (['1H', '2H'].includes(status.short)) return `${status.elapsed}'`;
    return status.short;
  };

  // Check if match is live
  const isLive = ['1H', '2H', 'HT'].includes(currentFixture.fixture.status.short);

  return (
    <div className="text-center px-4 min-w-[80px]">
      {currentFixture.fixture.status.short === 'NS' ? (
        <div className="text-sm text-muted-foreground">
          {getStatusDisplay()}
        </div>
      ) : (
        <div className="space-y-1">
          <div className="text-lg font-bold text-foreground">
            <div className={isLive ? 'text-green-600 dark:text-green-400' : ''}>
              {currentFixture.goals.home ?? 0}
            </div>
            <div className={isLive ? 'text-green-600 dark:text-green-400' : ''}>
              {currentFixture.goals.away ?? 0}
            </div>
          </div>
          <div className={`text-xs ${isLive ? 'text-green-600 dark:text-green-400 font-medium' : 'text-muted-foreground'}`}>
            {getStatusDisplay()}
            {isLive && (
              <span className="ml-1">
                <span className="inline-block w-1 h-1 bg-green-500 rounded-full animate-pulse"></span>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Hook to get live fixture data
export function useLiveFixtureData(fixtureId: string | number) {
  const { liveFixtures } = useLiveFixtures();
  
  return liveFixtures.find(
    fixture => fixture._id === fixtureId || fixture.fixture.id === fixtureId
  );
}
