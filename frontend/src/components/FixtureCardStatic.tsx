// Server Component for static fixture card content
import Image from 'next/image';
import { Fixture } from '@/lib/api';

interface FixtureCardStaticProps {
  fixture: Fixture;
  children?: React.ReactNode; // For interactive parts
}

export function FixtureCardStatic({ fixture, children }: FixtureCardStaticProps) {
  // Format time for display
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Get status display
  const getStatusDisplay = () => {
    const status = fixture.fixture.status;
    if (status.short === 'NS') return formatTime(fixture.fixture.timestamp);
    if (status.short === 'FT') return 'FT';
    if (status.short === 'HT') return 'HT';
    if (['1H', '2H'].includes(status.short)) return `${status.elapsed}'`;
    return status.short;
  };

  return (
    <div className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors border-b border-border last:border-b-0">
      {/* Home Team */}
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        <div className="w-8 h-8 flex-shrink-0">
          <Image
            src={fixture.teams.home.logo}
            alt={fixture.teams.home.name}
            width={32}
            height={32}
            className="w-8 h-8 object-contain"
            loading="lazy"
          />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate text-foreground">
            {fixture.teams.home.name}
          </p>
        </div>
      </div>

      {/* Score/Time Section */}
      <div className="text-center px-4 min-w-[80px]">
        {fixture.fixture.status.short === 'NS' ? (
          <div className="text-sm text-muted-foreground">
            {getStatusDisplay()}
          </div>
        ) : (
          <div className="space-y-1">
            <div className="text-lg font-bold text-foreground">
              <div>{fixture.goals.home ?? 0}</div>
              <div>{fixture.goals.away ?? 0}</div>
            </div>
            <div className="text-xs text-muted-foreground">
              {getStatusDisplay()}
            </div>
          </div>
        )}
      </div>

      {/* Away Team */}
      <div className="flex items-center space-x-3 flex-1 min-w-0 justify-end">
        <div className="flex-1 min-w-0 text-right">
          <p className="text-sm font-medium truncate text-foreground">
            {fixture.teams.away.name}
          </p>
        </div>
        <div className="w-8 h-8 flex-shrink-0">
          <Image
            src={fixture.teams.away.logo}
            alt={fixture.teams.away.name}
            width={32}
            height={32}
            className="w-8 h-8 object-contain"
            loading="lazy"
          />
        </div>
      </div>

      {/* Interactive parts rendered as children */}
      {children}
    </div>
  );
}
