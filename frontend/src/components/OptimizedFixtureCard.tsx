// Optimized Fixture Card combining Server and Client Components
import { Fixture } from '@/lib/api';
import { FixtureCardStatic } from './FixtureCardStatic';
import { FixtureCardInteractive } from './FixtureCardInteractive';
import { LiveScoreUpdater } from './LiveScoreUpdater';

interface OptimizedFixtureCardProps {
  fixture: Fixture;
}

// Generate SEO-friendly URL for fixture page
const generateFixtureUrl = (fixture: Fixture): string => {
  const homeTeam = fixture.teams.home.name.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
  const awayTeam = fixture.teams.away.name.toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');

  return `/match/${homeTeam}-vs-${awayTeam}/${fixture._id || fixture.fixture.id}`;
};

export function OptimizedFixtureCard({ fixture }: OptimizedFixtureCardProps) {
  return (
    <div className="relative">
      <FixtureCardStatic fixture={fixture}>
        {/* Live score updates - client component for real-time data */}
        <LiveScoreUpdater fixture={fixture} />

        {/* Interactive click handling */}
        <FixtureCardInteractive
          fixture={fixture}
          generateFixtureUrl={generateFixtureUrl}
        />
      </FixtureCardStatic>
    </div>
  );
}
