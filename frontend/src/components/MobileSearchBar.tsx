'use client';

import { useState } from 'react';
import { Search } from 'lucide-react';

export function MobileSearchBar() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="md:hidden mt-3">
      <div className="flex items-center bg-muted rounded-lg px-3 py-2">
        <Search className="w-4 h-4 text-muted-foreground mr-2" />
        <input
          type="text"
          placeholder="Search teams, leagues, countries..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
        />
      </div>
    </div>
  );
}
