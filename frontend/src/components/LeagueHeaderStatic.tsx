// Server Component for static league header content
import Image from 'next/image';
import { getCountryFlagUrl, handleFlagError } from '@/utils/countryFlags';

interface LeagueHeaderStaticProps {
  leagueCountry: string;
  leagueLogo: string;
  leagueName: string;
  children?: React.ReactNode; // For interactive parts
}

export function LeagueHeaderStatic({ 
  leagueCountry, 
  leagueLogo, 
  leagueName, 
  children 
}: LeagueHeaderStaticProps) {
  const flagUrl = getCountryFlagUrl(leagueCountry);

  return (
    <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
      <div className="w-[18px] h-[18px] flex-shrink-0 relative">
        {flagUrl ? (
          <Image
            src={flagUrl}
            alt={leagueCountry}
            width={18}
            height={18}
            className="w-[18px] h-[18px] object-cover rounded-full border border-border"
            loading="lazy"
            onError={(e) => {
              handleFlagError(e, leagueLogo);
              e.currentTarget.className = "w-[18px] h-[18px] object-cover rounded-full border border-border";
            }}
          />
        ) : (
          <Image
            src={leagueLogo}
            alt={leagueCountry}
            width={18}
            height={18}
            className="w-[18px] h-[18px] object-cover rounded-full border border-border"
            loading="lazy"
          />
        )}
      </div>
      <h3 className="text-sm font-medium text-foreground flex-1">
        {leagueName}
      </h3>
      {children}
    </div>
  );
}
