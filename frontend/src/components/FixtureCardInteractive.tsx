'use client';

import { useRouter } from 'next/navigation';
import { Fixture } from '@/lib/api';

interface FixtureCardInteractiveProps {
  fixture: Fixture;
  generateFixtureUrl: (fixture: Fixture) => string;
}

export function FixtureCardInteractive({ fixture, generateFixtureUrl }: FixtureCardInteractiveProps) {
  const router = useRouter();

  const handleFixtureClick = () => {
    const url = generateFixtureUrl(fixture);
    router.push(url);
  };

  return (
    <button
      onClick={handleFixtureClick}
      className="absolute inset-0 w-full h-full bg-transparent hover:bg-muted/20 transition-colors cursor-pointer"
      aria-label={`View match details for ${fixture.teams.home.name} vs ${fixture.teams.away.name}`}
    />
  );
}
