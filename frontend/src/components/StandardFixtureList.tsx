'use client';

import { useMemo } from 'react';
import { Fixture } from '@/lib/api';
import { OptimizedFixtureCard } from './OptimizedFixtureCard';
import { LeagueHeaderStatic } from './LeagueHeaderStatic';
import { groupFixturesByLeague } from '@/lib/leagueTiers';

interface StandardFixtureListProps {
  fixtures: Fixture[];
  searchQuery?: string;
  selectedLeagueId?: number | null;
  className?: string;
}

export function StandardFixtureList({ 
  fixtures, 
  searchQuery = '', 
  selectedLeagueId,
  className = '' 
}: StandardFixtureListProps) {
  
  // Filter and group fixtures
  const leagueGroups = useMemo(() => {
    let filteredFixtures = fixtures;

    // Filter by selected league
    if (selectedLeagueId) {
      filteredFixtures = fixtures.filter(fixture =>
        fixture.league.id === selectedLeagueId
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filteredFixtures = filteredFixtures.filter(fixture =>
        fixture.teams.home.name.toLowerCase().includes(query) ||
        fixture.teams.away.name.toLowerCase().includes(query) ||
        fixture.league.name.toLowerCase().includes(query) ||
        fixture.league.country.toLowerCase().includes(query)
      );
    }

    return groupFixturesByLeague(filteredFixtures);
  }, [fixtures, searchQuery, selectedLeagueId]);

  // Show empty state if no fixtures
  if (leagueGroups.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
        <div className="text-center">
          <p className="text-muted-foreground text-lg mb-2">No fixtures found</p>
          {searchQuery && (
            <p className="text-muted-foreground text-sm">
              Try adjusting your search terms
            </p>
          )}
          {selectedLeagueId && !searchQuery && (
            <p className="text-muted-foreground text-sm">
              No fixtures available for this league
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {leagueGroups.map((leagueGroup) => (
        <div 
          key={`${leagueGroup.leagueId}-${leagueGroup.leagueName}`} 
          className="bg-card rounded-lg border container-border overflow-hidden"
        >
          {/* League Header */}
          <LeagueHeaderStatic
            leagueCountry={leagueGroup.leagueCountry}
            leagueLogo={leagueGroup.leagueLogo}
            leagueName={leagueGroup.leagueName}
          />

          {/* Fixtures for this league */}
          <div className="divide-y divide-border">
            {leagueGroup.fixtures.map((fixture) => (
              <div key={fixture._id || fixture.fixture.id}>
                <OptimizedFixtureCard fixture={fixture} />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
